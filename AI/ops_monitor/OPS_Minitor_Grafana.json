{"name": "OPS-Minitor-Grafana", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "a2482091-eaad-46f3-b703-4fad0f03cd33", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"operation": "getAll", "filters": {"query": ""}}, "type": "n8n-nodes-base.grafana", "typeVersion": 1, "position": [220, 0], "id": "875ca3d2-4a06-4ff8-ae59-510b3e906e35", "name": "<PERSON><PERSON>", "credentials": {"grafanaApi": {"id": "Ecwntg0hPYeEDLfT", "name": "Grafana account"}}}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "bced9347-395c-4a81-83e6-6c85165749bc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "05e02a9421dabd20d1f06b1b8200138a27e3ea0dda7fbe144fe4915b736d008b"}, "id": "kd3iDnOMnTNDegOw", "tags": []}