{"name": "OPS-Minitor-Cloudwatch", "nodes": [{"parameters": {"functionCode": "// Parse the dashboard body JSON string into an object\nconst dashboardData = JSON.parse($input.item.json.DashboardBody);\n\n// Format the output for easier processing\nreturn {\n  json: {\n    dashboardName: $input.item.json.DashboardName,\n    dashboardArn: $input.item.json.DashboardArn,\n    dashboardBody: dashboardData,\n    // Extract widgets for easier access\n    widgets: dashboardData.widgets || [],\n    // Add timestamp for reference\n    retrievedAt: new Date().toISOString()\n  }\n};"}, "id": "13a54d2e-1b3d-4ae1-ad34-121b1ca1c6a1", "name": "Format Dashboard Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [820, -220], "notes": "Parses the dashboard JSON body and formats it for easier use in subsequent nodes"}, {"parameters": {"region": "ap-southeast-1", "service": "CloudWatch", "operation": "GetDashboard"}, "type": "n8n-nodes-aws-sdk-v3.AWSSDKWrapper", "typeVersion": 1, "position": [640, -220], "id": "f4787283-4ec8-4a1e-84e8-9a4b72a8aa08", "name": "AWS Service Request", "credentials": {"awsSdkWrapperCredentialsApi": {"id": "XCHImWEP1FWyMVeg", "name": "AWS SDK Wrapper Credentials account"}}}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [440, -220], "id": "641715bf-c062-40f3-a33f-0022b261d7dc", "name": "When clicking ‘Execute workflow’"}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "AWS Service Request", "type": "main", "index": 0}]]}, "AWS Service Request": {"main": [[{"node": "Format Dashboard Data", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "72713bae-0c77-44f9-a5a0-79f0117fdaff", "meta": {"templateCredsSetupCompleted": true, "instanceId": "05e02a9421dabd20d1f06b1b8200138a27e3ea0dda7fbe144fe4915b736d008b"}, "id": "2IyyBQV1nzQdVNAr", "tags": []}