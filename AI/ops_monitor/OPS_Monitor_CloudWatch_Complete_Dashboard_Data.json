{"name": "OPS-Monitor-CloudWatch-Complete-Dashboard-Data", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [200, 0], "id": "641715bf-c062-40f3-a33f-0022b261d7dc", "name": "When clicking 'Execute workflow'"}, {"parameters": {"region": "ap-southeast-1", "service": "CloudWatch", "operation": "GetDashboard", "requestHasInput": true, "requestInput": "{\n  \"DashboardName\": \"MemoryMonitor-CRM-production\"\n}", "version": "2010-08-01"}, "type": "n8n-nodes-aws-sdk-v3.AWSSDKWrapper", "typeVersion": 1, "position": [400, 0], "id": "get-dashboard-config", "name": "1. Get Dashboard Config", "credentials": {"awsSdkWrapperCredentialsApi": {"id": "XCHImWEP1FWyMVeg", "name": "AWS SDK Wrapper Credentials account"}}}, {"parameters": {"functionCode": "// Step 2: Parse dashboard body and extract metric definitions\nconst dashboardBody = JSON.parse($input.item.json.DashboardBody);\nconst widgets = dashboardBody.widgets || [];\n\n// Extract metric queries from all widgets\nconst metricQueries = [];\nlet queryId = 1;\n\nwidgets.forEach((widget, widgetIndex) => {\n  const properties = widget.properties || {};\n  \n  // Handle different widget types\n  if (properties.metrics) {\n    // Standard metric widgets\n    properties.metrics.forEach((metric, metricIndex) => {\n      if (Array.isArray(metric) && metric.length >= 2) {\n        const namespace = metric[0];\n        const metricName = metric[1];\n        \n        // Extract dimensions if present\n        const dimensions = {};\n        for (let i = 2; i < metric.length; i += 2) {\n          if (i + 1 < metric.length) {\n            dimensions[metric[i]] = metric[i + 1];\n          }\n        }\n        \n        metricQueries.push({\n          Id: `m${queryId++}`,\n          MetricStat: {\n            Metric: {\n              Namespace: namespace,\n              MetricName: metricName,\n              Dimensions: Object.keys(dimensions).map(key => ({\n                Name: key,\n                Value: dimensions[key]\n              }))\n            },\n            Period: 300,\n            Stat: \"Average\"\n          },\n          ReturnData: true\n        });\n      }\n    });\n  }\n});\n\n// Return the extracted metric queries\nreturn {\n  json: {\n    dashboardName: $input.item.json.DashboardName,\n    totalWidgets: widgets.length,\n    metricQueries: metricQueries,\n    extractedAt: new Date().toISOString()\n  }\n};"}, "id": "parse-dashboard", "name": "2. Parse Dashboard & Extract Metrics", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [600, 0], "notes": "Extracts metric definitions from dashboard widgets"}, {"parameters": {"region": "ap-southeast-1", "service": "CloudWatch", "operation": "GetMetricData", "requestHasInput": true, "requestInput": "{\n  \"MetricDataQueries\": {{ JSON.stringify($json.metricQueries) }},\n  \"StartTime\": \"{{ $now.minus({hours: 1}).toISO() }}\",\n  \"EndTime\": \"{{ $now.toISO() }}\",\n  \"ScanBy\": \"TimestampDescending\"\n}", "version": "2010-08-01"}, "type": "n8n-nodes-aws-sdk-v3.AWSSDKWrapper", "typeVersion": 1, "position": [800, 0], "id": "get-metric-data", "name": "3. Get Actual Metric Data", "credentials": {"awsSdkWrapperCredentialsApi": {"id": "XCHImWEP1FWyMVeg", "name": "AWS SDK Wrapper Credentials account"}}}, {"parameters": {"functionCode": "// Step 4: Format the actual metric data for easy consumption\nconst metricResults = $input.item.json.MetricDataResults || [];\nconst messages = $input.item.json.Messages || [];\n\n// Process each metric result\nconst processedMetrics = metricResults.map(result => {\n  const values = result.Values || [];\n  const timestamps = result.Timestamps || [];\n  \n  // Combine values with timestamps\n  const dataPoints = values.map((value, index) => ({\n    timestamp: timestamps[index],\n    value: value\n  })).sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));\n  \n  // Calculate statistics\n  const numericValues = values.filter(v => typeof v === 'number');\n  const stats = numericValues.length > 0 ? {\n    current: dataPoints.length > 0 ? dataPoints[dataPoints.length - 1].value : null,\n    average: numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length,\n    maximum: Math.max(...numericValues),\n    minimum: Math.min(...numericValues),\n    count: numericValues.length\n  } : null;\n  \n  return {\n    metricId: result.Id,\n    label: result.Label,\n    dataPoints: dataPoints,\n    statistics: stats,\n    statusCode: result.StatusCode\n  };\n});\n\n// Return formatted results\nreturn {\n  json: {\n    dashboardName: \"MemoryMonitor-CRM-production\",\n    retrievedAt: new Date().toISOString(),\n    timeRange: {\n      start: \"{{ $now.minus({hours: 1}).toISO() }}\",\n      end: \"{{ $now.toISO() }}\"\n    },\n    totalMetrics: processedMetrics.length,\n    metrics: processedMetrics,\n    messages: messages,\n    summary: {\n      hasData: processedMetrics.some(m => m.dataPoints.length > 0),\n      totalDataPoints: processedMetrics.reduce((sum, m) => sum + m.dataPoints.length, 0)\n    }\n  }\n};"}, "id": "format-results", "name": "4. Format Dashboard Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1000, 0], "notes": "Formats the actual metric data with statistics and summary"}], "pinData": {}, "connections": {"When clicking 'Execute workflow'": {"main": [[{"node": "1. Get Dashboard Config", "type": "main", "index": 0}]]}, "1. Get Dashboard Config": {"main": [[{"node": "2. Parse Dashboard & Extract Metrics", "type": "main", "index": 0}]]}, "2. Parse Dashboard & Extract Metrics": {"main": [[{"node": "3. Get Actual Metric Data", "type": "main", "index": 0}]]}, "3. Get Actual Metric Data": {"main": [[{"node": "4. Format Dashboard Data", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "72713bae-0c77-44f9-a5a0-79f0117fdaff", "meta": {"templateCredsSetupCompleted": true, "instanceId": "05e02a9421dabd20d1f06b1b8200138a27e3ea0dda7fbe144fe4915b736d008b"}, "id": "2IyyBQV1nzQdVNAr", "tags": []}